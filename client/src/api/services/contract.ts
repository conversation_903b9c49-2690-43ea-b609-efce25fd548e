import { API_URL } from '@/shared/config';
import { parseStrToJson } from '@/shared/utils/stringUtil';
import type { ResultResponse } from '@/types/common';
import {ResultUtil} from "@/shared/utils/resultUtil.ts";

const API_PATH = '/contract';

// 合同生成结果接口
export interface ContractGenerationResult {
  contract_markdown: string;
  selected_template_name: string;
}

export const contractApi = {
  /**
   * 生成合同预览 - 使用SSE流式接口
   * @param userInput 用户输入
   * @param onLog 日志回调函数
   * @returns 合同生成结果
   */
  async generateContractPreview(
    userInput: string,
    onLog: (log: string) => void,
  ): Promise<ResultResponse<ContractGenerationResult>> {
    return new Promise((resolve) => {
      const eventSource = new EventSource(
        `${API_URL}${API_PATH}/generate/stream?userInput=${encodeURIComponent(userInput)}`,
      );

      eventSource.onmessage = (event) => {
        // 检查是否是结束事件
        if (event.data === '[END]') {
          eventSource.close();
          return;
        }
        const resp = parseStrToJson(event.data, null);
        if (!resp) return; // 排除对象事件
        resp.log && onLog(resp.log);
        resolve(resp);
      };

      eventSource.onerror = () => {
        eventSource.close();
        onLog('SSE链接错误');
        resolve(ResultUtil.fail(''));
      };
    });
  },

  /**
   * 下载合同文档
   * @param markdownContent Markdown内容
   * @param contractName 合同名称
   */
  async downloadContract(
    markdownContent: string,
    contractName: string = 'contract',
  ): Promise<void> {
    try {
      // 创建Blob并下载
      const blob = new Blob([markdownContent], { type: 'text/markdown;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${contractName}.md`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      throw new Error('下载失败');
    }
  },
};
