import { type FC, memo } from 'react';

import { useController } from '@/shared/hooks/useController';
import { useObservableState } from '@/shared/hooks/useObservableState';

import { HomeController } from './HomeController';
import { ContractInputForm } from './components/ContractInputForm';
import { ContractPreview } from './components/ContractPreview';
import { GenerationLogs } from './components/GenerationLogs';
import { InitialInputForm } from './components/InitialInputForm';

// 初始状态组件 - 居中显示输入框
const InitialView: FC<{ controller: HomeController }> = memo(({ controller }) => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-6">
      <div className="text-center space-y-6 mb-12">
        <h1 className="text-5xl font-bold text-gray-900">智能合同生成助手</h1>
        <p className="text-xl text-gray-600">基于AI技术，快速生成专业合同文档</p>
      </div>

      <InitialInputForm controller={controller} />
    </div>
  );
});

// 分栏布局组件
const SplitLayoutView: FC<{ controller: HomeController }> = memo(({ controller }) => {
  return (
    <div className="container mx-auto p-6 space-y-8 max-w-7xl">
      <div className="text-center space-y-3 mb-8">
        <h1 className="text-4xl font-bold text-gray-900">智能合同生成助手</h1>
        <p className="text-lg text-gray-600">基于AI技术，快速生成专业合同文档</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 左侧：输入表单 */}
        <div className="space-y-6">
          <ContractInputForm controller={controller} />
          <GenerationLogs controller={controller} />
        </div>

        {/* 右侧：预览区域 */}
        <div className="h-full">
          <ContractPreview controller={controller} />
        </div>
      </div>
    </div>
  );
});

// 页面内容
const PageMain: FC<{ controller: HomeController }> = memo(({ controller }) => {
  const showSplitLayout = useObservableState(controller.showSplitLayout$, false);

  return (
    <div className="relative size-full overflow-hidden">
      {/* 初始视图 */}
      <div
        className={`absolute inset-0 transition-all duration-700 ease-in-out${
          showSplitLayout
            ? 'opacity-0 scale-95 pointer-events-none'
            : 'opacity-100 scale-100'
        }`}
      >
        <InitialView controller={controller} />
      </div>

      {/* 分栏视图 */}
      <div
        className={`absolute inset-0 transition-all duration-700 ease-in-out${
          showSplitLayout
            ? 'opacity-100 scale-100'
            : 'opacity-0 scale-105 pointer-events-none'
        }`}
      >
        <SplitLayoutView controller={controller} />
      </div>
    </div>
  );
});

// 主页面组件
const HomePage: FC = () => {
  const [controller] = useController(() => {
    const controller = new HomeController();
    return [controller, {}];
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <PageMain controller={controller} />
    </div>
  );
};

export default memo(HomePage);
