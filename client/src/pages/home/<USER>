import { BehaviorSubject } from 'rxjs';

import { contractApi } from '@/api/services/contract';

// 生成状态枚举
export enum GenerationStatus {
  IDLE = 'idle',
  GENERATING = 'generating',
  SUCCESS = 'success',
  ERROR = 'error',
}

// 合同生成结果接口
export interface ContractGenerationResult {
  contract_markdown: string;
  selected_template_name: string;
}

export class HomeController {
  // 基础状态
  public userInput$ = new BehaviorSubject<string>('');
  public generationStatus$ = new BehaviorSubject<GenerationStatus>(GenerationStatus.IDLE);
  public generationLogs$ = new BehaviorSubject<string[]>([]);
  public contractResult$ = new BehaviorSubject<ContractGenerationResult | null>(null);
  public errorMessage$ = new BehaviorSubject<string>('');

  public constructor() {}

  // 更新用户输入
  public updateUserInput(input: string): void {
    this.userInput$.next(input);
  }

  // 生成合同
  public async generateContract(): Promise<void> {
    let userInput = this.userInput$.getValue();
    if (!userInput.trim()) {
      userInput = '我需要一份数据保密协议，甲方是ABC科技公司，乙方是XYZ创新工作室';
      this.userInput$.next(userInput);
    }

    this.generationStatus$.next(GenerationStatus.GENERATING);
    this.generationLogs$.next([]);
    this.contractResult$.next(null);
    this.errorMessage$.next('');

    try {
      const response = await contractApi.generateContractPreview(userInput, (log: string) => {
        // 处理实时日志
        const currentLogs = this.generationLogs$.getValue();
        this.generationLogs$.next([...currentLogs, log]);
      });

      if (response.success) {
        this.contractResult$.next(response.data);
        this.generationStatus$.next(GenerationStatus.SUCCESS);
        const currentLogs = this.generationLogs$.getValue();
        this.generationLogs$.next([...currentLogs, '合同生成完成']);
      } else {
        this.errorMessage$.next(response.message || '生成失败');
        this.generationStatus$.next(GenerationStatus.ERROR);
      }
    } catch (error) {
      this.errorMessage$.next('生成过程中发生错误');
      this.generationStatus$.next(GenerationStatus.ERROR);
    }
  }

  // 下载合同
  public async downloadContract(): Promise<void> {
    // TODO
  }

  // 清空结果
  public clearResults(): void {
    this.contractResult$.next(null);
    this.generationLogs$.next([]);
    this.generationStatus$.next(GenerationStatus.IDLE);
  }

  // 销毁
  destroy() {
    this.userInput$.complete();
    this.generationStatus$.complete();
    this.generationLogs$.complete();
    this.contractResult$.complete();
  }
}
