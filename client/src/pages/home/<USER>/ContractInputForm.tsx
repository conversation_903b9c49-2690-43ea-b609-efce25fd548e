import { type FC, memo } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { useObservableState } from '@/shared/hooks/useObservableState';

import { GenerationStatus, type HomeController } from '../HomeController';

interface ContractInputFormProps {
  controller: HomeController;
}

export const ContractInputForm: FC<ContractInputFormProps> = memo(({ controller }) => {
  const userInput = useObservableState(controller.userInput$, '');
  const generationStatus = useObservableState(controller.generationStatus$, GenerationStatus.IDLE);
  const errorMessage = useObservableState(controller.errorMessage$, '');

  const isGenerating = generationStatus === GenerationStatus.GENERATING;

  const handleInputChange = (value: string) => {
    controller.updateUserInput(value);
  };

  const handleGenerate = () => {
    controller.generateContract();
  };

  return (
    <Card className="w-full shadow-md">
      <CardHeader className="bg-gray-50 border-b">
        <CardTitle>合同需求描述</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 pt-4">
        <div className="space-y-2">
          <Textarea
            placeholder="请描述您的合同需求，例如：我需要一份技术服务合同，甲方是ABC公司，乙方是XYZ科技有限公司，服务内容包括软件开发和技术支持..."
            value={userInput}
            onChange={(e) => handleInputChange(e.target.value)}
            rows={6}
            className="resize-none focus:ring-2 focus:ring-blue-500"
            disabled={isGenerating}
          />
          {errorMessage && <p className="text-sm text-red-600">{errorMessage}</p>}
        </div>

        <div className="flex gap-3">
          <Button
            onClick={handleGenerate}
            disabled={isGenerating}
            className="flex-1 bg-blue-600 hover:bg-blue-700"
          >
            {isGenerating ? '生成中...' : userInput.trim() ? '生成合同' : '使用示例生成'}
          </Button>
          <Button variant="outline" onClick={handleClear} disabled={isGenerating}>
            清空
          </Button>
          {!userInput.trim() && (
            <Button variant="outline" onClick={handleUseExample} disabled={isGenerating}>
              使用示例
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
});

ContractInputForm.displayName = 'ContractInputForm';
